(中文!中文!中文!答复)文件夹[docs/prompt/Markdown转换JSON/v5]下有两份报告(markdown格式和json格式)和一份JSON规范文档,其中json格式是基于markdown格式转换而来.现在,需要你帮我生成一份json报告模板和提示词,使大模型可以基于给定类似的markdown报告转换成对应的json格式.类似的markdown报告与给定的markdown报告具有相似的结构(标题含义,图表数据),但是部分章节可能缺失,因此提示词应该可以根据输入markdown报告动态跳过部分章节,绝对不能为报告完整性虚构数据

(中文!中文!中文!答复)重新明确一下需求:生成新的提示词文件,达到与[prom_价值测评_s3_md2json.md]相同的效果:
1,markdown_content部分:参考文件[小佳_output.md]里的内容作为
2,json_structure_definition部分:可以照搬(除非现有JSON结构无法满足界面要求,则需要你给出优化建议)
3,json_template部分:参考界面效果[对比界面内容提取.md],给出对应的JSON模板
4,system_prompt和user_prompt部分:参考[prom_价值测评_s3_md2json.md]相关部分,完成markdown_content+json_template=>json_report的转换


(中文!中文!中文!答复)请参考文档[docs/prompt/Markdown转换JSON/v4_progressive/step1_content_analysis.md]的分段结构,将提示词[docs/prompt/Markdown转换JSON/v5_基于JSON模板生成/ai_conversion_prompt.md]调整为[system_prompt\user_prompt\...]等分段格式